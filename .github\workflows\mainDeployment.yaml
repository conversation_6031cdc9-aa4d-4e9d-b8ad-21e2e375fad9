name: Deploy to PROD

on:
  push:
    branches:
      - test


jobs:
  DeployingOnDev:
    name: Deploy on main
    runs-on: ubuntu-latest
    steps:
      - name: Connecting to SSH and running script
        uses: appleboy/ssh-action@master
        with:
          host: **************
          username: root
          password: gar<PERSON><PERSON><PERSON><PERSON>@@ 
          script: |
            cd /path/to/your/project  # Ensure you're in the right directory
            chmod +x run.sh  # Ensure the script is executable
            ./run.sh  # Run your script
