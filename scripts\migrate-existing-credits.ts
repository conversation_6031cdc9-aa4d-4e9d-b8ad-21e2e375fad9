#!/usr/bin/env ts-node

/**
 * Existing Credits Migration Script
 * 
 * This script migrates existing user credits from the legacy system to the new
 * credit ledger system with proper expiration tracking.
 * 
 * Usage:
 * npm run migrate-credits
 * npm run migrate-credits -- --dry-run
 * npm run migrate-credits -- --user-id USER_ID
 */

import dotenv from 'dotenv';
import { PrismaClient, CreditTransactionType } from '@prisma/client';
import { addCreditsToLedger } from '../db/creditLedger';

dotenv.config();

const prisma = new PrismaClient();

interface MigrationOptions {
  dryRun: boolean;
  userId?: string;
  defaultExpirationDays: number;
}

interface MigrationResult {
  totalUsersProcessed: number;
  totalCreditsConverted: number;
  totalTransactionsCreated: number;
  errors: string[];
  executionTime: number;
}

function parseArgs(): MigrationOptions {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    userId: args.find(arg => arg.startsWith('--user-id='))?.split('=')[1],
    defaultExpirationDays: parseInt(
      args.find(arg => arg.startsWith('--expiration-days='))?.split('=')[1] || '365'
    ),
  };
}

async function migrateUserCredits(
  userId: string, 
  options: MigrationOptions
): Promise<{ creditsConverted: number; transactionsCreated: number }> {
  try {
    const user = await prisma.user.findUnique({
      where: { UserID: userId },
    });

    if (!user) {
      throw new Error(`User ${userId} not found`);
    }

    // Check if user already has credit transactions (already migrated)
    const existingTransactions = await prisma.creditTransaction.count({
      where: { userId },
    });

    if (existingTransactions > 0) {
      console.log(`⚠️  User ${userId} already has ${existingTransactions} credit transactions - skipping`);
      return { creditsConverted: 0, transactionsCreated: 0 };
    }

    const currentCredits = user.credits;
    
    if (currentCredits <= 0) {
      console.log(`ℹ️  User ${userId} has no credits to migrate`);
      return { creditsConverted: 0, transactionsCreated: 0 };
    }

    if (options.dryRun) {
      console.log(`🔍 DRY RUN: Would migrate ${currentCredits} credits for user ${userId}`);
      return { creditsConverted: currentCredits, transactionsCreated: 1 };
    }

    // Create expiration date based on options
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + options.defaultExpirationDays);

    // Create credit transaction for existing credits
    await addCreditsToLedger({
      userId,
      amount: currentCredits,
      type: CreditTransactionType.ADMIN_ADJUSTMENT,
      source: 'migration',
      sourceId: 'legacy-migration',
      description: `Migrated from legacy credit system - ${currentCredits} credits`,
      expiresAt: options.defaultExpirationDays > 0 ? expiresAt : undefined,
    });

    console.log(`✅ Migrated ${currentCredits} credits for user ${userId} (expires: ${expiresAt.toDateString()})`);
    
    return { creditsConverted: currentCredits, transactionsCreated: 1 };
  } catch (error: any) {
    console.error(`❌ Error migrating credits for user ${userId}:`, error.message);
    throw error;
  }
}

async function migrateAllUsers(options: MigrationOptions): Promise<MigrationResult> {
  const startTime = Date.now();
  const result: MigrationResult = {
    totalUsersProcessed: 0,
    totalCreditsConverted: 0,
    totalTransactionsCreated: 0,
    errors: [],
    executionTime: 0,
  };

  try {
    console.log('🚀 Starting migration of existing credits to ledger system...');

    // Get all users with credits > 0
    const usersWithCredits = await prisma.user.findMany({
      where: {
        credits: { gt: 0 },
      },
      select: {
        UserID: true,
        credits: true,
        name: true,
        email: true,
      },
    });

    console.log(`📊 Found ${usersWithCredits.length} users with credits to migrate`);

    if (options.dryRun) {
      console.log('🔍 DRY RUN MODE - No changes will be made\n');
    }

    // Process each user
    for (const user of usersWithCredits) {
      try {
        const userResult = await migrateUserCredits(user.UserID, options);
        
        result.totalUsersProcessed++;
        result.totalCreditsConverted += userResult.creditsConverted;
        result.totalTransactionsCreated += userResult.transactionsCreated;

        // Progress indicator
        if (result.totalUsersProcessed % 10 === 0) {
          console.log(`📈 Progress: ${result.totalUsersProcessed}/${usersWithCredits.length} users processed`);
        }
      } catch (error: any) {
        const errorMsg = `Error migrating user ${user.UserID} (${user.email}): ${error.message}`;
        console.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }

    result.executionTime = Date.now() - startTime;

    console.log('\n✅ Migration completed');
    return result;
  } catch (error: any) {
    const errorMsg = `Migration failed: ${error.message}`;
    console.error(errorMsg);
    result.errors.push(errorMsg);
    result.executionTime = Date.now() - startTime;
    return result;
  }
}

async function validateMigration(): Promise<void> {
  console.log('🔍 Validating migration...');

  try {
    // Get total credits from users table
    const userCreditsSum = await prisma.user.aggregate({
      _sum: { credits: true },
    });

    // Get total credits from credit transactions
    const transactionCreditsSum = await prisma.creditTransaction.aggregate({
      where: {
        type: {
          in: [
            CreditTransactionType.PURCHASE_SUBSCRIPTION,
            CreditTransactionType.PURCHASE_PAYG,
            CreditTransactionType.ADMIN_ADJUSTMENT,
            CreditTransactionType.BONUS,
            CreditTransactionType.REFUND,
          ],
        },
      },
      _sum: { remainingAmount: true },
    });

    const userTotal = userCreditsSum._sum.credits || 0;
    const transactionTotal = transactionCreditsSum._sum.remainingAmount || 0;

    console.log(`📊 Validation Results:`);
    console.log(`   • Total credits in users table: ${userTotal}`);
    console.log(`   • Total credits in transactions: ${transactionTotal}`);
    console.log(`   • Difference: ${Math.abs(userTotal - transactionTotal)}`);

    if (Math.abs(userTotal - transactionTotal) < 0.01) {
      console.log('✅ Migration validation passed - credit totals match');
    } else {
      console.log('⚠️  Migration validation warning - credit totals do not match exactly');
    }
  } catch (error: any) {
    console.error('❌ Migration validation failed:', error.message);
  }
}

async function main(): Promise<void> {
  console.log('🔄 SearchLeads Credit Migration Tool\n');

  // Validate environment
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is required');
    process.exit(1);
  }

  const options = parseArgs();

  try {
    let result: MigrationResult;

    if (options.userId) {
      // Migrate specific user
      console.log(`🎯 Migrating credits for user: ${options.userId}`);
      const userResult = await migrateUserCredits(options.userId, options);
      
      result = {
        totalUsersProcessed: 1,
        totalCreditsConverted: userResult.creditsConverted,
        totalTransactionsCreated: userResult.transactionsCreated,
        errors: [],
        executionTime: 0,
      };
    } else {
      // Migrate all users
      result = await migrateAllUsers(options);
    }

    // Show results
    console.log('\n📊 MIGRATION RESULTS:');
    console.log(`   • Users processed: ${result.totalUsersProcessed}`);
    console.log(`   • Credits converted: ${result.totalCreditsConverted}`);
    console.log(`   • Transactions created: ${result.totalTransactionsCreated}`);
    console.log(`   • Execution time: ${result.executionTime}ms`);
    
    if (result.errors.length > 0) {
      console.log(`   • Errors: ${result.errors.length}`);
      result.errors.forEach(error => console.log(`     - ${error}`));
    }

    // Validate migration if not dry run
    if (!options.dryRun && result.totalUsersProcessed > 0) {
      console.log('');
      await validateMigration();
    }

    console.log('\n✅ Credit migration process completed');
    
  } catch (error: any) {
    console.error('\n❌ Credit migration process failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Show help if requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
SearchLeads Credit Migration Tool

This tool migrates existing user credits from the legacy system to the new
credit ledger system with proper expiration tracking.

Usage:
  npm run migrate-credits                           # Migrate all users
  npm run migrate-credits -- --dry-run             # Show what would be migrated
  npm run migrate-credits -- --user-id=USER_ID     # Migrate specific user
  npm run migrate-credits -- --expiration-days=30  # Set custom expiration

Options:
  --dry-run                    Show what would be migrated without making changes
  --user-id=USER_ID           Migrate credits for specific user only
  --expiration-days=DAYS      Set expiration days for migrated credits (default: 365, 0 = no expiration)
  --help, -h                  Show this help message

Examples:
  npm run migrate-credits -- --dry-run
  npm run migrate-credits -- --user-id=user123
  npm run migrate-credits -- --expiration-days=30
  npm run migrate-credits -- --dry-run --expiration-days=0

Note: Users who already have credit transactions will be skipped to prevent duplicate migration.
`);
  process.exit(0);
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

export { main as migrateCreditScript };
