name: Pushing To Master

on:
    push:
        branches:
            - master

jobs:
    check:
        runs-on: ubuntu-latest

        steps:
            # Checkout code
            -
                name: Checkout code
                uses: actions/checkout@v4

            # Set up Node.js
            - 
                name: Set up Node.js
                uses: actions/setup-node@v3
                with:
                    node-version: 20 # Replace with your Node.js version

            # Install dependencies
            -
                name: Install dependencies
                run: npm ci