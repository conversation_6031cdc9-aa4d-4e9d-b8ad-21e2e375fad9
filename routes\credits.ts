import express, { Request, Response } from 'express';
import { 
  getUserCredits, 
  getCreditStats, 
  getExpiringCredits,
  addAdminCredits,
  addBonusCredits,
  refundCredits 
} from '../db/creditManager';
import { getCreditHistory } from '../db/creditLedger';
import { getCreditExpirationStats } from '../jobs/creditExpiration';
import userAuth from '../middleware/supabaseAuth';

const app = express.Router();

/**
 * Credit Management and Monitoring Routes
 */

// Get user's detailed credit information
app.get('/balance', userAuth, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    
    const result = await getUserCredits(userId);
    
    if (result.error) {
      return res.status(500).json({ error: result.error });
    }

    res.status(200).json({
      balance: result.balance,
      user: {
        UserID: result.user?.UserID,
        credits: result.user?.credits,
        TotalCreditsBought: result.user?.TotalCreditsBought,
        TotalCreditsUsed: result.user?.TotalCreditsUsed,
      },
    });
  } catch (error: any) {
    console.error('Error getting credit balance:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get user's credit transaction history
app.get('/history', userAuth, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const history = await getCreditHistory(userId, limit, offset);

    res.status(200).json({ history });
  } catch (error: any) {
    console.error('Error getting credit history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get user's credit statistics
app.get('/stats', userAuth, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    
    const stats = await getCreditStats(userId);
    
    if (stats.error) {
      return res.status(500).json({ error: stats.error });
    }

    res.status(200).json({ stats });
  } catch (error: any) {
    console.error('Error getting credit stats:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get credits expiring soon for user
app.get('/expiring', userAuth, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const withinDays = parseInt(req.query.days as string) || 7;

    const expiringCredits = await getExpiringCredits(userId, withinDays);

    res.status(200).json({
      expiringCredits: expiringCredits.expiringCredits,
      totalExpiring: expiringCredits.totalExpiring,
      withinDays,
    });
  } catch (error: any) {
    console.error('Error getting expiring credits:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin: Add credits to user account
app.post('/admin/add', async (req: Request, res: Response) => {
  try {
    // TODO: Add admin authentication middleware
    const { userId, amount, description, expirationDays } = req.body;

    if (!userId || !amount || !description) {
      return res.status(400).json({ 
        error: 'userId, amount, and description are required' 
      });
    }

    const result = await addAdminCredits(userId, amount, description, expirationDays);

    if (result.success) {
      res.status(200).json({ 
        message: 'Credits added successfully',
        transaction: result.transaction 
      });
    } else {
      res.status(500).json({ error: result.error });
    }
  } catch (error: any) {
    console.error('Error adding admin credits:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin: Add bonus credits
app.post('/admin/bonus', async (req: Request, res: Response) => {
  try {
    // TODO: Add admin authentication middleware
    const { userId, amount, description, expirationDays } = req.body;

    if (!userId || !amount || !description) {
      return res.status(400).json({ 
        error: 'userId, amount, and description are required' 
      });
    }

    const result = await addBonusCredits(
      userId, 
      amount, 
      description, 
      expirationDays || 90
    );

    if (result.success) {
      res.status(200).json({ 
        message: 'Bonus credits added successfully',
        transaction: result.transaction 
      });
    } else {
      res.status(500).json({ error: result.error });
    }
  } catch (error: any) {
    console.error('Error adding bonus credits:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin: Refund credits
app.post('/admin/refund', async (req: Request, res: Response) => {
  try {
    // TODO: Add admin authentication middleware
    const { userId, amount, description, originalTransactionId } = req.body;

    if (!userId || !amount || !description) {
      return res.status(400).json({ 
        error: 'userId, amount, and description are required' 
      });
    }

    const result = await refundCredits(userId, amount, description, originalTransactionId);

    if (result.success) {
      res.status(200).json({ 
        message: 'Credits refunded successfully',
        transaction: result.transaction 
      });
    } else {
      res.status(500).json({ error: result.error });
    }
  } catch (error: any) {
    console.error('Error refunding credits:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin: Get system-wide credit expiration statistics
app.get('/admin/expiration-stats', async (req: Request, res: Response) => {
  try {
    // TODO: Add admin authentication middleware
    const stats = await getCreditExpirationStats();

    res.status(200).json({ stats });
  } catch (error: any) {
    console.error('Error getting expiration stats:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin: Get user's detailed credit information
app.get('/admin/user/:userId', async (req: Request, res: Response) => {
  try {
    // TODO: Add admin authentication middleware
    const { userId } = req.params;

    const [creditInfo, history, stats] = await Promise.all([
      getUserCredits(userId),
      getCreditHistory(userId, 100),
      getCreditStats(userId),
    ]);

    if (creditInfo.error) {
      return res.status(500).json({ error: creditInfo.error });
    }

    res.status(200).json({
      user: creditInfo.user,
      balance: creditInfo.balance,
      history,
      stats,
    });
  } catch (error: any) {
    console.error('Error getting user credit info:', error);
    res.status(500).json({ error: error.message });
  }
});

// Health check endpoint for credit system
app.get('/health', async (req: Request, res: Response) => {
  try {
    const stats = await getCreditExpirationStats();
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      systemStats: {
        totalExpiringCredits: stats.totalExpiringCredits,
        usersWithExpiringCredits: stats.usersWithExpiringCredits,
        nextExpirationDate: stats.nextExpirationDate,
      },
    });
  } catch (error: any) {
    console.error('Credit system health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

export default app;
