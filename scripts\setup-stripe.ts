#!/usr/bin/env ts-node

/**
 * Stripe Setup Script
 * 
 * This script sets up Stripe products and prices for the SearchLeads application.
 * It creates both subscription and pay-as-you-go products with their respective pricing.
 * 
 * Usage:
 * npm run setup-stripe
 * 
 * Requirements:
 * - STRIPE_PUBLIC_SECRET_KEY must be set in .env
 * - Database must be accessible
 */

import dotenv from 'dotenv';
import { setupStripeProducts } from '../utils/stripeSetup';

// Load environment variables
dotenv.config();

async function main() {
  console.log('🚀 Starting Stripe setup...\n');

  // Validate environment variables
  if (!process.env.STRIPE_PUBLIC_SECRET_KEY) {
    console.error('❌ Error: STRIPE_PUBLIC_SECRET_KEY is not set in .env file');
    process.exit(1);
  }

  if (!process.env.DATABASE_URL) {
    console.error('❌ Error: DATABASE_URL is not set in .env file');
    process.exit(1);
  }

  try {
    await setupStripeProducts();
    
    console.log('\n✅ Stripe setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   • Created subscription product with 5 pricing tiers');
    console.log('   • Created pay-as-you-go product with standard pricing');
    console.log('   • Updated .env file with product and price IDs');
    console.log('   • Saved all products and prices to database');
    
    console.log('\n💡 Next steps:');
    console.log('   1. Restart your application to load new environment variables');
    console.log('   2. Test the subscription flow using the API endpoints');
    console.log('   3. Configure your Stripe webhook endpoint');
    
    console.log('\n🔗 Subscription API endpoints:');
    console.log('   • POST /api/subscription/create - Create new subscription');
    console.log('   • GET  /api/subscription/current - Get current subscription');
    console.log('   • POST /api/subscription/cancel - Cancel subscription');
    console.log('   • GET  /api/subscription/plans - Get available plans');
    
  } catch (error) {
    console.error('\n❌ Stripe setup failed:', error);
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

export { main as setupStripe };
