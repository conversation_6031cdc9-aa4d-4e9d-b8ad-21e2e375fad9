import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

/**
 * Stripe Configuration Utility
 * 
 * This module ensures all Stripe IDs are loaded dynamically from environment variables
 * or database, preventing any hardcoded Stripe IDs in the application.
 */

export interface StripeConfig {
  subscriptionProductId: string | null;
  paygProductId: string | null;
  subscriptionPrices: {
    [credits: string]: string; // e.g., "10000": "price_xxx"
  };
  paygPrices: {
    [credits: string]: string;
  };
}

let cachedConfig: StripeConfig | null = null;
let configLastUpdated: number = 0;
const CONFIG_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get Stripe configuration with caching
 */
export async function getStripeConfig(): Promise<StripeConfig> {
  const now = Date.now();
  
  // Return cached config if still valid
  if (cachedConfig && (now - configLastUpdated) < CONFIG_CACHE_TTL) {
    return cachedConfig;
  }

  // Load fresh configuration
  const config = await loadStripeConfig();
  cachedConfig = config;
  configLastUpdated = now;
  
  return config;
}

/**
 * Load Stripe configuration from database and environment variables
 */
async function loadStripeConfig(): Promise<StripeConfig> {
  try {
    // Get products from database
    const products = await prisma.stripeProduct.findMany({
      where: { active: true },
      include: {
        prices: {
          where: { active: true },
        },
      },
    });

    const config: StripeConfig = {
      subscriptionProductId: null,
      paygProductId: null,
      subscriptionPrices: {},
      paygPrices: {},
    };

    // Process products and prices
    for (const product of products) {
      const isSubscription = product.name.toLowerCase().includes('subscription');
      
      if (isSubscription) {
        config.subscriptionProductId = product.id;
      } else {
        config.paygProductId = product.id;
      }

      // Process prices for this product
      for (const price of product.prices) {
        const creditsKey = price.credits.toString();
        
        if (price.interval === 'month') {
          config.subscriptionPrices[creditsKey] = price.id;
        } else {
          config.paygPrices[creditsKey] = price.id;
        }
      }
    }

    return config;
  } catch (error) {
    console.error('Error loading Stripe configuration:', error);
    throw new Error('Failed to load Stripe configuration');
  }
}

/**
 * Get subscription price ID by credits amount
 */
export async function getSubscriptionPriceId(credits: number): Promise<string | null> {
  const config = await getStripeConfig();
  return config.subscriptionPrices[credits.toString()] || null;
}

/**
 * Get pay-as-you-go price ID by credits amount
 */
export async function getPaygPriceId(credits: number): Promise<string | null> {
  const config = await getStripeConfig();
  return config.paygPrices[credits.toString()] || null;
}

/**
 * Get product ID by type
 */
export async function getProductId(isSubscription: boolean): Promise<string | null> {
  const config = await getStripeConfig();
  return isSubscription ? config.subscriptionProductId : config.paygProductId;
}

/**
 * Validate that all required Stripe configuration is available
 */
export async function validateStripeConfig(): Promise<{
  isValid: boolean;
  errors: string[];
}> {
  const errors: string[] = [];

  // Check environment variables
  if (!process.env.STRIPE_PUBLIC_SECRET_KEY) {
    errors.push('STRIPE_PUBLIC_SECRET_KEY is not set');
  }

  if (!process.env.STRIPE_WEBHOOK_SECRET) {
    errors.push('STRIPE_WEBHOOK_SECRET is not set');
  }

  try {
    const config = await getStripeConfig();

    // Check products
    if (!config.subscriptionProductId) {
      errors.push('Subscription product not found in database');
    }

    if (!config.paygProductId) {
      errors.push('Pay-as-you-go product not found in database');
    }

    // Check subscription prices
    const requiredSubscriptionCredits = [10000, 20000, 30000, 40000, 50000];
    for (const credits of requiredSubscriptionCredits) {
      if (!config.subscriptionPrices[credits.toString()]) {
        errors.push(`Subscription price for ${credits} credits not found`);
      }
    }

    // Check pay-as-you-go prices
    const requiredPaygCredits = [10000];
    for (const credits of requiredPaygCredits) {
      if (!config.paygPrices[credits.toString()]) {
        errors.push(`Pay-as-you-go price for ${credits} credits not found`);
      }
    }

  } catch (error) {
    errors.push(`Failed to load configuration: ${error}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Clear configuration cache (useful for testing or after updates)
 */
export function clearConfigCache(): void {
  cachedConfig = null;
  configLastUpdated = 0;
}

/**
 * Get all available subscription plans with pricing
 */
export async function getAvailableSubscriptionPlans(): Promise<Array<{
  credits: number;
  priceId: string;
  amount: number;
  savings: number;
}>> {
  try {
    const prices = await prisma.stripePrice.findMany({
      where: {
        active: true,
        interval: 'month',
      },
      include: {
        product: true,
      },
      orderBy: {
        credits: 'asc',
      },
    });

    return prices.map(price => ({
      credits: price.credits,
      priceId: price.id,
      amount: price.unitAmount,
      savings: Math.round((price.credits * 3) - price.unitAmount), // vs $3 per 1K credits
    }));
  } catch (error) {
    console.error('Error getting subscription plans:', error);
    return [];
  }
}

/**
 * Get all available pay-as-you-go options
 */
export async function getAvailablePaygOptions(): Promise<Array<{
  credits: number;
  priceId: string;
  amount: number;
}>> {
  try {
    const prices = await prisma.stripePrice.findMany({
      where: {
        active: true,
        interval: null,
      },
      include: {
        product: true,
      },
      orderBy: {
        credits: 'asc',
      },
    });

    return prices.map(price => ({
      credits: price.credits,
      priceId: price.id,
      amount: price.unitAmount,
    }));
  } catch (error) {
    console.error('Error getting pay-as-you-go options:', error);
    return [];
  }
}
