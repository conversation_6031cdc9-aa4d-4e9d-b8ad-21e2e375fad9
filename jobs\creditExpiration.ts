import { PrismaClient } from '@prisma/client';
import { expireCredits } from '../db/creditLedger';
import { getUserCredits } from '../db/creditManager';

const prisma = new PrismaClient();

/**
 * Credit Expiration Job System
 * 
 * This module handles automated credit expiration, cleanup, and user notifications.
 */

export interface ExpirationJobResult {
  totalUsersProcessed: number;
  totalCreditsExpired: number;
  totalTransactionsExpired: number;
  errors: string[];
  executionTime: number;
}

/**
 * Main credit expiration job - expires all credits that have passed their expiration date
 */
export async function runCreditExpirationJob(): Promise<ExpirationJobResult> {
  const startTime = Date.now();
  const result: ExpirationJobResult = {
    totalUsersProcessed: 0,
    totalCreditsExpired: 0,
    totalTransactionsExpired: 0,
    errors: [],
    executionTime: 0,
  };

  try {
    console.log('🕒 Starting credit expiration job...');

    // Get all users with potentially expired credits
    const usersWithExpiredCredits = await prisma.creditTransaction.findMany({
      where: {
        expiresAt: { lte: new Date() },
        remainingAmount: { gt: 0 },
        type: {
          in: [
            'PURCHASE_SUBSCRIPTION',
            'PURCHASE_PAYG',
            'ADMIN_ADJUSTMENT',
            'BONUS',
            'REFUND',
          ],
        },
      },
      select: { userId: true },
      distinct: ['userId'],
    });

    console.log(`📊 Found ${usersWithExpiredCredits.length} users with expired credits`);

    // Process each user
    for (const userRecord of usersWithExpiredCredits) {
      try {
        const expirationResult = await expireCredits(userRecord.userId);
        
        result.totalUsersProcessed++;
        result.totalCreditsExpired += expirationResult.totalExpiredCredits;
        result.totalTransactionsExpired += expirationResult.expiredTransactions;

        if (expirationResult.totalExpiredCredits > 0) {
          console.log(`💸 Expired ${expirationResult.totalExpiredCredits} credits for user ${userRecord.userId}`);
        }
      } catch (error: any) {
        const errorMsg = `Error expiring credits for user ${userRecord.userId}: ${error.message}`;
        console.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }

    result.executionTime = Date.now() - startTime;

    console.log('✅ Credit expiration job completed');
    console.log(`📈 Results: ${result.totalUsersProcessed} users, ${result.totalCreditsExpired} credits expired`);

    return result;
  } catch (error: any) {
    const errorMsg = `Credit expiration job failed: ${error.message}`;
    console.error(errorMsg);
    result.errors.push(errorMsg);
    result.executionTime = Date.now() - startTime;
    return result;
  }
}

/**
 * Send expiration warnings to users with credits expiring soon
 */
export async function sendExpirationWarnings(withinDays: number = 7): Promise<{
  usersNotified: number;
  errors: string[];
}> {
  const result = {
    usersNotified: 0,
    errors: [],
  };

  try {
    console.log(`📧 Checking for credits expiring within ${withinDays} days...`);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + withinDays);

    // Find users with credits expiring soon
    const expiringCredits = await prisma.creditTransaction.findMany({
      where: {
        expiresAt: {
          gte: new Date(),
          lte: cutoffDate,
        },
        remainingAmount: { gt: 0 },
        type: {
          in: [
            'PURCHASE_SUBSCRIPTION',
            'PURCHASE_PAYG',
            'ADMIN_ADJUSTMENT',
            'BONUS',
            'REFUND',
          ],
        },
      },
      include: {
        user: {
          select: {
            UserID: true,
            email: true,
            name: true,
          },
        },
      },
    });

    // Group by user
    const userExpirations = new Map<string, { user: any; totalExpiring: number; expiresAt: Date }>();

    for (const credit of expiringCredits) {
      const userId = credit.userId;
      const existing = userExpirations.get(userId);
      
      if (existing) {
        existing.totalExpiring += credit.remainingAmount;
        if (credit.expiresAt && credit.expiresAt < existing.expiresAt) {
          existing.expiresAt = credit.expiresAt;
        }
      } else {
        userExpirations.set(userId, {
          user: credit.user,
          totalExpiring: credit.remainingAmount,
          expiresAt: credit.expiresAt || new Date(),
        });
      }
    }

    // Send notifications (implement your notification system here)
    for (const [userId, expiration] of userExpirations) {
      try {
        await sendExpirationNotification(expiration.user, expiration.totalExpiring, expiration.expiresAt);
        result.usersNotified++;
        console.log(`📧 Sent expiration warning to ${expiration.user.email}`);
      } catch (error: any) {
        const errorMsg = `Failed to send notification to user ${userId}: ${error.message}`;
        console.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }

    console.log(`✅ Expiration warnings sent to ${result.usersNotified} users`);
    return result;
  } catch (error: any) {
    const errorMsg = `Failed to send expiration warnings: ${error.message}`;
    console.error(errorMsg);
    result.errors.push(errorMsg);
    return result;
  }
}

/**
 * Send expiration notification to a user (implement your notification system)
 */
async function sendExpirationNotification(
  user: { UserID: string; email: string; name: string },
  creditsExpiring: number,
  expiresAt: Date
): Promise<void> {
  // TODO: Implement your notification system (email, SMS, in-app notification)
  // For now, just log the notification
  console.log(`📧 NOTIFICATION: ${user.name} (${user.email}) has ${creditsExpiring} credits expiring on ${expiresAt.toDateString()}`);
  
  // Example implementation:
  // await emailService.send({
  //   to: user.email,
  //   subject: 'Your SearchLeads credits are expiring soon',
  //   template: 'credit-expiration-warning',
  //   data: {
  //     name: user.name,
  //     creditsExpiring,
  //     expiresAt: expiresAt.toDateString(),
  //   },
  // });
}

/**
 * Get credit expiration statistics
 */
export async function getCreditExpirationStats(): Promise<{
  totalExpiredCredits: number;
  totalExpiringCredits: number;
  usersWithExpiringCredits: number;
  nextExpirationDate: Date | null;
}> {
  try {
    const now = new Date();
    const next30Days = new Date();
    next30Days.setDate(next30Days.getDate() + 30);

    // Get expired credits
    const expiredCredits = await prisma.creditTransaction.aggregate({
      where: {
        type: 'EXPIRATION',
        createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
      },
      _sum: { amount: true },
    });

    // Get expiring credits (next 30 days)
    const expiringCredits = await prisma.creditTransaction.aggregate({
      where: {
        expiresAt: {
          gte: now,
          lte: next30Days,
        },
        remainingAmount: { gt: 0 },
        type: {
          in: [
            'PURCHASE_SUBSCRIPTION',
            'PURCHASE_PAYG',
            'ADMIN_ADJUSTMENT',
            'BONUS',
            'REFUND',
          ],
        },
      },
      _sum: { remainingAmount: true },
    });

    // Get users with expiring credits
    const usersWithExpiringCredits = await prisma.creditTransaction.findMany({
      where: {
        expiresAt: {
          gte: now,
          lte: next30Days,
        },
        remainingAmount: { gt: 0 },
        type: {
          in: [
            'PURCHASE_SUBSCRIPTION',
            'PURCHASE_PAYG',
            'ADMIN_ADJUSTMENT',
            'BONUS',
            'REFUND',
          ],
        },
      },
      select: { userId: true },
      distinct: ['userId'],
    });

    // Get next expiration date
    const nextExpiration = await prisma.creditTransaction.findFirst({
      where: {
        expiresAt: { gte: now },
        remainingAmount: { gt: 0 },
        type: {
          in: [
            'PURCHASE_SUBSCRIPTION',
            'PURCHASE_PAYG',
            'ADMIN_ADJUSTMENT',
            'BONUS',
            'REFUND',
          ],
        },
      },
      orderBy: { expiresAt: 'asc' },
      select: { expiresAt: true },
    });

    return {
      totalExpiredCredits: Math.abs(expiredCredits._sum.amount || 0),
      totalExpiringCredits: expiringCredits._sum.remainingAmount || 0,
      usersWithExpiringCredits: usersWithExpiringCredits.length,
      nextExpirationDate: nextExpiration?.expiresAt || null,
    };
  } catch (error: any) {
    console.error('Error getting credit expiration stats:', error);
    throw new Error(error.message);
  }
}

/**
 * Schedule credit expiration job (call this from your scheduler)
 */
export function scheduleCreditExpirationJob(): void {
  // Run every hour
  setInterval(async () => {
    try {
      await runCreditExpirationJob();
    } catch (error) {
      console.error('Scheduled credit expiration job failed:', error);
    }
  }, 60 * 60 * 1000); // 1 hour

  // Run expiration warnings daily at 9 AM
  setInterval(async () => {
    const now = new Date();
    if (now.getHours() === 9 && now.getMinutes() === 0) {
      try {
        await sendExpirationWarnings(7); // 7 days warning
      } catch (error) {
        console.error('Scheduled expiration warning job failed:', error);
      }
    }
  }, 60 * 1000); // Check every minute

  console.log('📅 Credit expiration jobs scheduled');
}
