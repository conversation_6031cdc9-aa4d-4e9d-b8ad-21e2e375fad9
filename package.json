{"name": "search-leads", "version": "1.0.0", "description": "", "main": "server.ts", "scripts": {"dev": "nodemon --exec ts-node ./server.ts", "postinstall": "npx prisma generate --schema ./prisma/schema.prisma", "migrate": "npx prisma migrate dev --name init", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "start": "node dist/server.js", "prebuild": "npm run install-prisma-and-copy", "install-prisma-and-copy": "npm run postinstall && npm run copy-prisma", "copy-prisma": "mkdir -p dist/prisma && cp -r ./prisma/* ./dist/prisma", "setup-stripe": "ts-node scripts/setup-stripe.ts", "validate-setup": "ts-node scripts/validate-setup.ts", "expire-credits": "ts-node scripts/expire-credits.ts", "migrate-credits": "ts-node scripts/migrate-existing-credits.ts", "test-credit-expiration": "ts-node scripts/test-credit-expiration.ts", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:reset": "npx prisma migrate reset"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.717.0", "@prisma/client": "^6.11.1", "@supabase/supabase-js": "^2.45.1", "aws-sdk": "^2.1692.0", "better-sqlite3": "^11.3.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "form-data": "^4.0.0", "quick.db": "^9.1.7", "stripe": "^17.5.0", "twilio": "^5.3.2", "uuid": "^10.0.0"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.2.0", "@types/uuid": "^10.0.0", "prisma": "^6.11.1", "typescript": "^5.5.4"}}