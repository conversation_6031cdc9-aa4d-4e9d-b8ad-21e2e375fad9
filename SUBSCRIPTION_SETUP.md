# SearchLeads Subscription System Setup

This document provides a comprehensive guide for setting up and using the SearchLeads subscription system with Stripe integration.

## Overview

The subscription system provides:
- **Subscription Plans**: $20 per 10K credits (monthly recurring)
- **Pay-as-you-go**: $30 per 10K credits (one-time payment) - existing pricing maintained
- **Tiered Pricing**: 10K, 20K, 30K, 40K, 50K credit options
- **Automatic Credit Renewal**: Credits added monthly for active subscriptions
- **Webhook Integration**: Real-time subscription status updates

## Prerequisites

1. **Stripe Account**: Active Stripe account with API keys
2. **Database**: PostgreSQL database (Supabase or local)
3. **Environment Variables**: Properly configured .env file

## Setup Instructions

### 1. Environment Configuration

Update your `.env` file with the following variables:

```env
# Stripe Configuration
STRIPE_PUBLIC_SECRET_KEY="sk_test_..." # Your Stripe secret key
STRIPE_WEBHOOK_SECRET="whsec_..."      # Stripe webhook endpoint secret

# Database
DATABASE_URL="postgresql://..."
DIRECT_URL="postgresql://..."

# Auto-populated by setup script
STRIPE_SUBSCRIPTION_PRODUCT_ID=""
STRIPE_PAYG_PRODUCT_ID=""
STRIPE_SUBSCRIPTION_10K_PRICE_ID=""
STRIPE_SUBSCRIPTION_20K_PRICE_ID=""
STRIPE_SUBSCRIPTION_30K_PRICE_ID=""
STRIPE_SUBSCRIPTION_40K_PRICE_ID=""
STRIPE_SUBSCRIPTION_50K_PRICE_ID=""
STRIPE_PAYG_10K_PRICE_ID=""
```

### 2. Database Migration

Run the database migration to create subscription tables:

```bash
npm run db:migrate
```

### 3. Stripe Products Setup

Run the setup script to create Stripe products and prices:

```bash
npm run setup-stripe
```

This script will:
- Create subscription and pay-as-you-go products in Stripe
- Create pricing tiers for each product
- Save product/price information to your database
- Update .env file with Stripe IDs

### 4. Webhook Configuration

1. In your Stripe Dashboard, go to Webhooks
2. Create a new webhook endpoint: `https://yourdomain.com/api/payments/searchLeadsConfirmPayment`
3. Select these events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `payment_intent.succeeded`
4. Copy the webhook secret to your .env file

## API Endpoints

### Subscription Management

#### Create Subscription
```http
POST /api/subscription/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "credits": 10000,
  "paymentMethodId": "pm_..."
}
```

#### Get Current Subscription
```http
GET /api/subscription/current
Authorization: Bearer <token>
```

#### Cancel Subscription
```http
POST /api/subscription/cancel
Authorization: Bearer <token>
```

#### Reactivate Subscription
```http
POST /api/subscription/reactivate
Authorization: Bearer <token>
```

#### Get Available Plans
```http
GET /api/subscription/plans
```

### User Information (Enhanced)

#### Get User with Subscription
```http
GET /api/user/getUser
Authorization: Bearer <token>
```

Response includes subscription information:
```json
{
  "user": {
    "UserID": "...",
    "name": "...",
    "email": "...",
    "credits": 15000,
    "subscription": {
      "id": "sub_...",
      "status": "active",
      "currentPeriodStart": "2024-01-01T00:00:00Z",
      "currentPeriodEnd": "2024-02-01T00:00:00Z",
      "cancelAtPeriodEnd": false,
      "credits": 10000,
      "amount": 2000,
      "interval": "month",
      "planName": "10K Credits/month"
    }
  }
}
```

## Pricing Structure

### Subscription Plans (Monthly)
- **10K Credits**: $20/month (33% savings vs pay-as-you-go)
- **20K Credits**: $40/month (33% savings vs pay-as-you-go)
- **30K Credits**: $60/month (33% savings vs pay-as-you-go)
- **40K Credits**: $80/month (33% savings vs pay-as-you-go)
- **50K Credits**: $100/month (33% savings vs pay-as-you-go)

### Pay-as-you-go (One-time)
- **10K Credits**: $30 (existing pricing maintained)

## Webhook Events

The system handles these Stripe webhook events:

- **`payment_intent.succeeded`**: Processes one-time credit purchases
- **`customer.subscription.created`**: Logs new subscription creation
- **`customer.subscription.updated`**: Updates subscription status/billing cycle
- **`customer.subscription.deleted`**: Marks subscription as canceled
- **`invoice.payment_succeeded`**: Adds credits for subscription renewals
- **`invoice.payment_failed`**: Marks subscription as past due

## Database Schema

### New Tables

#### StripeProduct
- `id`: Stripe product ID
- `name`: Product name
- `description`: Product description
- `active`: Whether product is active

#### StripePrice
- `id`: Stripe price ID
- `productId`: Reference to StripeProduct
- `unitAmount`: Price in cents
- `currency`: Currency (USD)
- `interval`: Billing interval (month/null)
- `credits`: Number of credits provided

#### Subscription
- `id`: Internal subscription ID
- `userId`: Reference to User
- `stripeSubscriptionId`: Stripe subscription ID
- `stripePriceId`: Reference to StripePrice
- `status`: Subscription status
- `currentPeriodStart/End`: Billing period dates
- `cancelAtPeriodEnd`: Cancellation flag

### Updated Tables

#### User
- Added `stripeCustomerId`: Stripe customer ID

## Testing

### Test Credit Flow
1. Create a subscription using test payment method
2. Verify credits are added to user account
3. Test subscription renewal (simulate webhook)
4. Test subscription cancellation

### Test Webhook Events
Use Stripe CLI to forward webhooks to local development:

```bash
stripe listen --forward-to localhost:5050/api/payments/searchLeadsConfirmPayment
```

## Troubleshooting

### Common Issues

1. **Webhook signature verification fails**
   - Ensure STRIPE_WEBHOOK_SECRET is correct
   - Check webhook endpoint URL

2. **Products not created**
   - Verify STRIPE_PUBLIC_SECRET_KEY is valid
   - Check database connection
   - Run setup script with proper permissions

3. **Credits not added on renewal**
   - Check webhook event handling
   - Verify subscription status in database
   - Check Stripe dashboard for failed invoices

### Logs

Monitor application logs for:
- Webhook event processing
- Subscription creation/updates
- Credit additions
- Error messages

## Security Considerations

1. **API Keys**: Never expose Stripe secret keys in client-side code
2. **Webhook Verification**: Always verify webhook signatures
3. **User Authentication**: Ensure proper authentication on all endpoints
4. **Database Access**: Use environment variables for database credentials

## Support

For issues or questions:
1. Check application logs
2. Verify Stripe dashboard for payment/subscription status
3. Review webhook delivery logs in Stripe
4. Check database for subscription records
