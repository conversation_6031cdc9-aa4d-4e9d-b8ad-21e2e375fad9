name: Deploy to Development

on:
    push:
        branches: 
        - dev

jobs:
    ToDockerhub:
        name: Build and push Docker image
        runs-on: ubuntu-latest
        steps:
        -
            name: Checkout
            uses: actions/checkout@v4
        -
            name: Login to Docker Hub
            uses: docker/login-action@v3
            with:
                username: ${{ secrets.DOCKERHUB_USERNAME }}
                password: ${{ secrets.DOCKERHUB_PASSWORD }}
        -
            name: Set up QEMU
            uses: docker/setup-qemu-action@v3
        -
            name: Set up Docker Buildx
            uses: docker/setup-buildx-action@v3
        -
            name: Build and push
            uses: docker/build-push-action@v6
            with:
                context: .
                push: true
                tags: devanshhealdns/searchleadsbackend:dev
                
    DeployingOnDev:
        name: Deploy on Development
        needs: ToDockerhub
        runs-on: ubuntu-latest
        steps:
        -
            name: connecting to ssh
            uses: appleboy/ssh-action@master
            with:
                host: ${{ secrets.SSH_HOST }}
                username: ${{ secrets.SSH_USERNAME }}
                password: ${{ secrets.SSH_PASSWORD }}
                script: bash ./run.sh