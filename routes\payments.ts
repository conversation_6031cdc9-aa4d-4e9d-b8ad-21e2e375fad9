import express, { Request, Response } from 'express';
import Stripe from 'stripe';
import { addCredits } from '../db/user';
import {
  createSubscription,
  updateSubscription,
  getSubscriptionByStripeId,
  processSubscriptionRenewal
} from '../db/subscription';
import userAuth from "../middleware/supabaseAuth";
import { stripeClient } from "../payments/stripe";
import { StripePaymentMetadata } from '../types/interfaces';

const app = express.Router();

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET as string;

app.post("/searchLeadsConfirmPayment", async (req: Request, res: Response) => {
    try {
        // Webhook route
        try {
            const sig = req.headers['stripe-signature'];

            if (!sig) {
                return res.status(200).json({ error: 'Missing Stripe signature' });
            }

            let event: Stripe.Event;

            const payload = req.body;
            const payloadString = JSON.stringify(payload, null, 2);
            const secret = endpointSecret;
            const header = stripeClient.webhooks.generateTestHeaderString({
                payload: payloadString,
                secret,
            })

            event = stripeClient.webhooks.constructEvent(payloadString, header, secret);

            // Handle different webhook events
            switch (event.type) {
                case "payment_intent.succeeded":
                    await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
                    break;

                case "customer.subscription.created":
                    await handleSubscriptionCreated(event.data.object as Stripe.Subscription);
                    break;

                case "customer.subscription.updated":
                    await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
                    break;

                case "customer.subscription.deleted":
                    await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
                    break;

                case "invoice.payment_succeeded":
                    await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
                    break;

                case "invoice.payment_failed":
                    await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
                    break;

                default:
                    console.log(`Unhandled event type: ${event.type}`);
            }
            return res.status(200).json({ error: 'Internal Server Error' });
        } catch (error: any) {
            console.error('Unexpected error:', error);
            return res.status(200).json({ error: `Internal Server Error ${error.message}` });
        }
    } catch (error: any) {
        res.status(200).json({ message: error.message });
    }
})

app.post("/paymentCancelledIntent", userAuth, async (req: Request, res: Response) => {
    try {
        const { paymentIntentId, cancellationReason } = req.body;
        const paymentIntent = await stripeClient.paymentIntents.cancel(paymentIntentId, {
            cancellation_reason: cancellationReason, // reason to cancel
        });
        res.status(200).json({ paymentIntent });
        
    } catch (error: any) {
        res.status(500).json({ message: error.message });
    }
})

app.post("/retrieveCoupon", userAuth, async (req: Request, res: Response) => {
    try {
        const { couponCode } = req.body;
        const coupon = await stripeClient.coupons.retrieve(couponCode);
        res.status(200).json({ coupon });
    } catch (error: any) {
        res.status(500).json({ message: error.message });
    }
})

app.post("/deleteCustomer", userAuth, async (req: Request, res: Response) => {
    try {
        const { customerId } = req.body;
        const deletedCustomer = await stripeClient.customers.deleteDiscount(customerId);
        res.status(200).json({ deletedCustomer });
    } catch (error: any) {
        res.status(500).json({ message: error.message });
    }
})

app.post("/updateCouponCode", userAuth, async (req: Request, res: Response) => {
    try {
        const { customerId, couponCode } = req.body;
        const updatedCustomer = await stripeClient.customers.update(customerId, {
            coupon: couponCode
        });

        res.status(200).json({ updatedCustomer });
    } catch (error: any) {
        res.status(500).json({ message: error.message });
    }
})

app.post("/createCustomer", userAuth, async (req: Request, res: Response) => {
    try {
        const { name, email, couponID } = req.body;
        const customer = await stripeClient.customers.create({
            name: name,
            email: email,
            coupon: couponID
        })

        res.status(200).json({ customer });
    } catch (error: any) {
        res.status(500).json({ message: error.message });
    }
})

app.post("/createPaymentIntent", userAuth, async (req: Request, res: Response) => {
    try {
        const {amount, currency,costumerID,description,automaticPayment, referral,credits,userID,cientName} = req.body;

        const paymentIntent = await stripeClient.paymentIntents.create({
            amount: amount,
            currency: currency,
            customer: costumerID,
            description: description,
            automatic_payment_methods: {
                enabled: automaticPayment
            },
            metadata: {
                _afficoneRef: referral || null,
                credits: credits,
                currency: currency,
                userId: userID,
                clientName: cientName
            },
        });
        
        res.status(200).json({ paymentIntent });
    } catch (error: any) {
        res.status(500).json({ message: error.message });        
    }
});

app.post("/findCustomerByEmail", userAuth, async (req: Request, res: Response) => {
    try {
        const { email } = req.body;
        const customers = await stripeClient.customers.list({
            email: email,
            limit: 1, // Limit to 1 result for efficiency
        });

        if (customers.data.length > 0) {
            res.status(200).json({ customerId: customers.data[0].id });
        } else {
            res.status(404).json({ message: "Customer not found" });
        }
    } catch (error: any) {
        res.status(500).json({ message: error.message });
    }
});

// Webhook handler functions
async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
    try {
        if (paymentIntent.description === "Payment for EnrichMinion Credits") {
            console.log("Payment for EnrichMinion - skipping");
            return;
        }

        const metadata = paymentIntent.metadata as unknown as StripePaymentMetadata;

        if (metadata && !metadata.subscriptionPlan) {
            console.log("Processing one-time payment");
            if (!metadata.credits || !metadata.userId) {
                console.error('Missing credits or userId in metadata');
                return;
            }

            const updatedCredits = await addCredits(parseFloat(metadata.credits), metadata.userId);
            if (!updatedCredits) {
                console.error('Failed to add credits to user');
                return;
            }

            console.log(`Added ${metadata.credits} credits to user ${metadata.userId}`);
        }
    } catch (error) {
        console.error('Error handling payment intent succeeded:', error);
    }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
    try {
        console.log(`Subscription created: ${subscription.id}`);
        // Subscription creation is handled in the API endpoint
        // This webhook is mainly for logging and monitoring
    } catch (error) {
        console.error('Error handling subscription created:', error);
    }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
    try {
        console.log(`Subscription updated: ${subscription.id}`);

        await updateSubscription(subscription.id, {
            status: subscription.status,
            currentPeriodStart: new Date(subscription.current_period_start * 1000),
            currentPeriodEnd: new Date(subscription.current_period_end * 1000),
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
        });

        console.log(`Updated subscription ${subscription.id} in database`);
    } catch (error) {
        console.error('Error handling subscription updated:', error);
    }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
    try {
        console.log(`Subscription deleted: ${subscription.id}`);

        await updateSubscription(subscription.id, {
            status: 'canceled',
        });

        console.log(`Marked subscription ${subscription.id} as canceled in database`);
    } catch (error) {
        console.error('Error handling subscription deleted:', error);
    }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
    try {
        console.log(`Invoice payment succeeded: ${invoice.id}`);

        if (invoice.subscription && invoice.billing_reason === 'subscription_cycle') {
            // This is a subscription renewal
            const subscription = await getSubscriptionByStripeId(invoice.subscription as string);
            if (subscription) {
                const creditsToAdd = subscription.price.credits;
                await processSubscriptionRenewal(subscription.stripeSubscriptionId, creditsToAdd);
                console.log(`Added ${creditsToAdd} credits for subscription renewal`);
            }
        }
    } catch (error) {
        console.error('Error handling invoice payment succeeded:', error);
    }
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
    try {
        console.log(`Invoice payment failed: ${invoice.id}`);

        if (invoice.subscription) {
            const subscription = await getSubscriptionByStripeId(invoice.subscription as string);
            if (subscription) {
                // Update subscription status to past_due
                await updateSubscription(subscription.stripeSubscriptionId, {
                    status: 'past_due',
                });
                console.log(`Marked subscription ${subscription.stripeSubscriptionId} as past_due`);
            }
        }
    } catch (error) {
        console.error('Error handling invoice payment failed:', error);
    }
}

export default app;

