import express from 'express';
import userRoutes from './user';
import logsRoutes from './logs';
import adminRoutes from './admin';
import apiKeyRoutes from './apikey';
import serviceRoutes from './service';
import billingRoutes from './billing';
import paymentRoutes from "./payments";
import subscriptionRoutes from "./subscription";
import creditRoutes from "./credits";

const app = express.Router();

app.use("/user", userRoutes);
app.use("/logs", logsRoutes);
app.use("/admin", adminRoutes);
app.use("/v1",apiKeyRoutes);
app.use("/service",serviceRoutes);
app.use("/billing",billingRoutes);
app.use("/payments",paymentRoutes);
app.use("/subscription",subscriptionRoutes);
app.use("/credits",creditRoutes);

export default app;
