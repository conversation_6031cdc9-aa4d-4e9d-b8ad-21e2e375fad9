generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model BillingDetails {
  BillingID        String   @id @unique
  userID           String
  Url              String
  CreditsRequested Int
  date             DateTime
  user             User     @relation(fields: [userID], references: [UserID])
}

model Logs {
  LogID          String   @id @unique
  userID         String
  name           String?
  email          String?
  leadsRequested Int
  leadsEnriched  Int?
  apolloLink     String
  fileName       String
  creditsUsed    Float
  url            String?
  status         String
  date           DateTime
  user           User     @relation(fields: [userID], references: [UserID])
}

model User {
  UserID             String              @id @unique
  name               String
  email              String
  companyName        String?
  phoneNumber        String?
  location           String?
  credits            Float               // Legacy field - will be calculated from CreditTransaction
  heardFrom          String?
  apikey             String?             @unique
  date               DateTime            @default(now())
  TotalCreditsBought Float              @default(0)
  TotalCreditsUsed   Float              @default(0)
  stripeCustomerId   String?             @unique
  isActive           Boolean             @default(true)
  isSuspended        Boolean             @default(false)
  suspendedAt        DateTime?
  suspendedReason    String?
  lastLoginAt        DateTime?
  logs               Logs[]
  BillingDetails     BillingDetails[]
  subscriptions      Subscription[]
  creditTransactions CreditTransaction[]
  auditLogs          AuditLog[]
}

model Admin {
  id          String      @id @unique @default(cuid())
  email       String      @unique
  password    String
  name        String
  role        AdminRole   @default(ADMIN)
  permissions Json?       // Flexible permissions object
  isActive    Boolean     @default(true)
  lastLoginAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  auditLogs   AuditLog[]
}

model AuditLog {
  id          String    @id @unique @default(cuid())
  adminId     String?
  userId      String?   // For user-related actions
  action      String    // e.g., "user_created", "credit_added", "subscription_cancelled"
  resource    String    // e.g., "user", "subscription", "credit"
  resourceId  String?   // ID of the affected resource
  details     Json?     // Additional details about the action
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime  @default(now())
  admin       Admin?    @relation(fields: [adminId], references: [id])
  user        User?     @relation(fields: [userId], references: [UserID])

  @@index([adminId, createdAt])
  @@index([action, createdAt])
  @@index([resource, resourceId])
}

enum AdminRole {
  SUPER_ADMIN
  ADMIN
  SUPPORT
  ANALYST
  BILLING
}

model StripeProduct {
  id          String       @id @unique
  name        String
  description String?
  active      Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  prices      StripePrice[]
}

model StripePrice {
  id            String        @id @unique
  productId     String
  unitAmount    Int
  currency      String        @default("usd")
  interval      String?       // null for one-time, "month" for subscription
  intervalCount Int?          @default(1)
  credits       Int           // Number of credits this price provides
  active        Boolean       @default(true)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  product       StripeProduct @relation(fields: [productId], references: [id])
  subscriptions Subscription[]
}

model Subscription {
  id                   String      @id @unique
  userId               String
  stripeSubscriptionId String      @unique
  stripePriceId        String
  status               String      // active, canceled, past_due, etc.
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean     @default(false)
  createdAt            DateTime    @default(now())
  updatedAt            DateTime    @updatedAt
  user                 User        @relation(fields: [userId], references: [UserID])
  price                StripePrice @relation(fields: [stripePriceId], references: [id])
}

model CreditTransaction {
  id              String                @id @unique @default(cuid())
  userId          String
  amount          Float                 // Positive for additions, negative for usage
  type            CreditTransactionType
  source          String?               // e.g., "subscription", "payg", "admin", "bonus"
  sourceId        String?               // e.g., subscription ID, payment intent ID
  description     String?
  expiresAt       DateTime?             // When these credits expire (null = never)
  usedAt          DateTime?             // When these credits were used (for usage records)
  remainingAmount Float                 @default(0) // For partial usage tracking
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  user            User                  @relation(fields: [userId], references: [UserID])

  @@index([userId, expiresAt])
  @@index([userId, type, createdAt])
  @@index([expiresAt])
}

enum CreditTransactionType {
  PURCHASE_SUBSCRIPTION
  PURCHASE_PAYG
  USAGE
  EXPIRATION
  ADMIN_ADJUSTMENT
  BONUS
  REFUND
}
